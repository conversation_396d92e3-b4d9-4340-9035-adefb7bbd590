<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calorie Calculator - SuppRadar</title>
    <meta name="description" content="Bereken je dagelijkse caloriebehoefte met onze geavanceerde calorie calculator. Inclusief BMR, TDEE en macro berekeningen.">

    <!-- DNS prefetching voor betere prestaties -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- Geoptimaliseerde Google Fonts - Open Sans met alle benodigde weights -->
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="main.css">

    <!-- Structured Data for FAQ -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": [
            {
                "@type": "Question",
                "name": "Wat is BMR (Basaal Metabolisme)?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "BMR staat voor Basal Metabolic Rate en is het aantal calorieën dat je lichaam nodig heeft om basisfuncties uit te voeren zoals ademhaling, bloedcirculatie en celvernieuwing, terwijl je in rust bent."
                }
            },
            {
                "@type": "Question",
                "name": "Wat is TDEE (Totale Dagelijkse Energiebehoefte)?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "TDEE is je BMR vermenigvuldigd met je activiteitsniveau. Dit geeft je totale caloriebehoefte per dag, inclusief beweging en dagelijkse activiteiten."
                }
            },
            {
                "@type": "Question",
                "name": "Hoe nauwkeurig zijn deze berekeningen?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Onze calculator gebruikt bewezen formules zoals Mifflin-St Jeor en Harris-Benedict. De resultaten zijn een goede schatting, maar individuele verschillen kunnen voorkomen. Luister altijd naar je lichaam."
                }
            },
            {
                "@type": "Question",
                "name": "Waarom is spiermassa belangrijk?",
                "acceptedAnswer": {
                    "@type": "Answer",
                    "text": "Spiermassa verhoogt je BMR omdat spierweefsel meer energie verbruikt dan vetweefsel, zelfs in rust. Mensen met meer spiermassa hebben daarom een hogere caloriebehoefte."
                }
            }
        ]
    }
    </script>

    <!-- Structured Data for Products -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "ItemList",
        "name": "Aanbevolen Supplementen",
        "itemListElement": [
            {
                "@type": "Product",
                "position": 1,
                "name": "Whey Protein Isolaat",
                "description": "Hoogwaardige eiwitpoeder voor spieropbouw en herstel",
                "category": "Eiwitten",
                "brand": "SuppRadar",
                "offers": {
                    "@type": "Offer",
                    "price": "29.99",
                    "priceCurrency": "EUR",
                    "availability": "https://schema.org/InStock"
                }
            },
            {
                "@type": "Product",
                "position": 2,
                "name": "Creatine Monohydraat",
                "description": "Voor meer kracht en explosieve energie tijdens training",
                "category": "Creatine",
                "brand": "SuppRadar",
                "offers": {
                    "@type": "Offer",
                    "price": "19.99",
                    "priceCurrency": "EUR",
                    "availability": "https://schema.org/InStock"
                }
            },
            {
                "@type": "Product",
                "position": 3,
                "name": "Multivitamine Complex",
                "description": "Complete dagelijkse vitamine en mineralen ondersteuning",
                "category": "Vitaminen",
                "brand": "SuppRadar",
                "offers": {
                    "@type": "Offer",
                    "price": "24.99",
                    "priceCurrency": "EUR",
                    "availability": "https://schema.org/InStock"
                }
            }
        ]
    }
    </script>


</head>
<body class="calculator-page">
    <header class="header">
        <div class="mobile-menu-toggle" id="mobile-menu-toggle">
            <span></span>
            <span></span>
            <span></span>
        </div>
        <div class="logo-container">
            <a href="./index.html" class="logo-link">
                <img src="./Afbeeldingen/Logo.webp" alt="SuppRadar Logo">
                <h1>SuppRadar</h1>
            </a>
        </div>
        <nav class="nav-menu" id="nav-menu">
            <a href="./index.html">Home</a>
            <a href="./supplementen.html">Supplementen</a>
            <a href="./calorie-calculator.html" class="active">Calorie Calculator</a>
            <div class="dropdown">
                <a href="./shop-per-doel.html">Shop per Doel</a>
                <div class="dropdown-content">
                    <a href="./shop-per-doel.html">Gewichtsverlies</a>
                    <a href="./shop-per-doel.html">Spieropbouw</a>
                    <a href="./shop-per-doel.html">Uithoudingsvermogen</a>
                    <a href="./shop-per-doel.html">Fitness & Onderhoud</a>
                    <a href="./shop-per-doel.html">Gezondheid & Focus</a>
                    <a href="./shop-per-doel.html">Topprestaties</a>
                </div>
            </div>
            <a href="./accessoires.html">Accessoires</a>
            <a href="#">Oefeningen</a>
            <a href="./blog.html">Blog</a>
        </nav>
    </header>

    <div class="content-container">
        <!-- HELE LAYOUT MET 3 KOLOMMEN - SUPPLEMENTEN STIJL -->
        <div style="display: flex; gap: 20px;">
            <!-- FAQ Sidebar (Links) -->
            <div style="width: 250px; background-color: var(--loblolly); border: 2px solid var(--nevada); border-radius: 10px; box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1); padding: 20px; flex-shrink: 0;">
                <h2>Veelgestelde vragen</h2>
                <div class="faq-item">
                    <h3>Wat is BMR?</h3>
                    <p>BMR staat voor Basal Metabolic Rate: het aantal calorieën dat je lichaam verbrandt in rust om basisfuncties uit te voeren zoals ademen en je hartslag.</p>
                </div>
                <div class="faq-item">
                    <h3>Wat is TDEE?</h3>
                    <p>TDEE is je Totale Dagelijkse Energieverbruik. Dat is je BMR vermenigvuldigd met je activiteitsniveau – dit getal geeft aan hoeveel calorieën je per dag nodig hebt om op gewicht te blijven.</p>
                </div>
                <div class="faq-item">
                    <h3>Hoeveel calorieën moet ik eten om af te vallen?</h3>
                    <p>Een goed uitgangspunt is om 20% onder je TDEE te gaan zitten. Dit zorgt gemiddeld voor ongeveer een halve kilo vetverlies per week.</p>
                </div>
                <div class="faq-item">
                    <h3>Hoe nauwkeurig is deze calculator?</h3>
                    <p>De calculator geeft een goede schatting op basis van wetenschappelijk onderbouwde formules. Het blijft echter een inschatting – luister altijd ook naar je lichaam.</p>
                </div>
                <div class="faq-item">
                    <h3>Kan ik spieren opbouwen én vet verliezen?</h3>
                    <p>Dat is mogelijk, vooral voor beginners of mensen die terugkeren na een pauze. Zorg dan voor een licht calorie-tekort en voldoende eiwitten.</p>
                </div>
            </div>

            <!-- Calculator (Midden) -->
            <div style="flex: 1; background-color: var(--loblolly); border: 2px solid var(--nevada); border-radius: 10px; box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1); padding: 20px; display: flex; flex-direction: column;">
                <!-- Clean Header -->
                <div style="text-align: center; margin-bottom: 20px;">
                    <h1 style="color: var(--black-pearl); margin: 0; font-size: 24px; font-weight: 700;">Calorie Calculator</h1>
                    <p style="color: var(--nevada); margin: 5px 0 0 0; font-size: 14px;">Bereken je dagelijkse caloriebehoefte</p>
                </div>

                <!-- PROMINENTE TOTALE CALORIEËN -->
                <div id="main-result" style="text-align: center; margin-bottom: 20px; padding: 20px; background: var(--white); border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); border: 2px solid var(--granny-smith); display: none;">
                    <div style="font-size: 16px; color: var(--nevada); margin-bottom: 8px; font-weight: 600;">🎯 Je dagelijkse caloriebehoefte</div>
                    <div id="target-calories" style="font-size: 32px; font-weight: 700; color: var(--black-pearl); margin-bottom: 8px;"></div>
                    <div id="goal-text" style="font-size: 14px; color: var(--nevada); font-style: italic;"></div>
                </div>

                <!-- Ondersteunende Resultaten -->
                <div id="bmr-tdee-results" style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px; display: none;">
                    <div style="text-align: center; padding: 15px; background: rgba(255,255,255,0.7); border-radius: 8px; border: 1px solid var(--nevada);">
                        <div style="font-size: 12px; color: var(--black-pearl); font-weight: 600; margin-bottom: 4px;">BMR</div>
                        <div style="font-size: 10px; color: var(--nevada); margin-bottom: 6px;">Basaal Metabolisme</div>
                        <div id="bmr-value" style="font-size: 16px; font-weight: 700; color: var(--black-pearl);"></div>
                    </div>
                    <div style="text-align: center; padding: 15px; background: rgba(255,255,255,0.7); border-radius: 8px; border: 1px solid var(--nevada);">
                        <div style="font-size: 12px; color: var(--black-pearl); font-weight: 600; margin-bottom: 4px;">TDEE</div>
                        <div style="font-size: 10px; color: var(--nevada); margin-bottom: 6px;">Totale Dagelijkse Energiebehoefte</div>
                        <div id="tdee-value" style="font-size: 16px; font-weight: 700; color: var(--black-pearl);"></div>
                    </div>
                </div>

                <!-- Macros Container -->
                <div id="macros-container" class="macros-container" style="margin-bottom: 20px; font-size: 12px; display: none;"></div>

                <!-- Clean Form Section -->
                <div style="background: rgba(255,255,255,0.5); padding: 20px; border-radius: 12px; border: 1px solid var(--nevada);">
                    <h3 style="color: var(--black-pearl); margin-bottom: 16px; font-size: 18px; font-weight: 700; text-align: center;">📝 Vul je gegevens in</h3>

                    <form id="calorie-form" style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 16px;">
                        <!-- Persoonlijke Gegevens Fieldset -->
                        <fieldset style="grid-column: 1 / -1; border: 1px solid var(--nevada); border-radius: 8px; padding: 16px; margin-bottom: 16px; background: rgba(255,255,255,0.3);">
                            <legend style="color: var(--black-pearl); font-weight: 700; font-size: 16px; padding: 0 8px;">👤 Persoonlijke Gegevens</legend>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 16px;">
                                <div style="display: flex; flex-direction: column;">
                                    <label for="gender" style="font-size: 13px; color: var(--black-pearl); margin-bottom: 6px; font-weight: 600;">Geslacht</label>
                                    <select id="gender" name="gender" required aria-label="Selecteer je geslacht" style="padding: 10px; border: 2px solid var(--nevada); border-radius: 6px; font-size: 14px; background: var(--white); transition: border-color 0.3s;">
                                        <option value="">Selecteer</option>
                                        <option value="male">Man</option>
                                        <option value="female">Vrouw</option>
                                    </select>
                                </div>
                                <div style="display: flex; flex-direction: column;">
                                    <label for="age" style="font-size: 13px; color: var(--black-pearl); margin-bottom: 6px; font-weight: 600;">Leeftijd (jaren)</label>
                                    <input type="number" id="age" name="age" min="10" max="120" placeholder="25" required aria-label="Voer je leeftijd in jaren in" style="padding: 10px; border: 2px solid var(--nevada); border-radius: 6px; font-size: 14px; background: var(--white); transition: border-color 0.3s;">
                                </div>
                                <div style="display: flex; flex-direction: column;">
                                    <label for="height" style="font-size: 13px; color: var(--black-pearl); margin-bottom: 6px; font-weight: 600;">Lengte (cm)</label>
                                    <input type="number" id="height" name="height" min="100" max="250" placeholder="175" required aria-label="Voer je lengte in centimeters in" style="padding: 10px; border: 2px solid var(--nevada); border-radius: 6px; font-size: 14px; background: var(--white); transition: border-color 0.3s;">
                                </div>
                            </div>
                        </fieldset>

                        <!-- Lichaamssamenstelling Fieldset -->
                        <fieldset style="grid-column: 1 / -1; border: 1px solid var(--nevada); border-radius: 8px; padding: 16px; margin-bottom: 16px; background: rgba(255,255,255,0.3);">
                            <legend style="color: var(--black-pearl); font-weight: 700; font-size: 16px; padding: 0 8px;">⚖️ Lichaamssamenstelling</legend>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                                <div style="display: flex; flex-direction: column;">
                                    <label for="weight" style="font-size: 13px; color: var(--black-pearl); margin-bottom: 6px; font-weight: 600;">Gewicht (kg)</label>
                                    <input type="number" id="weight" name="weight" min="30" max="300" step="0.1" placeholder="70" required aria-label="Voer je gewicht in kilogram in" style="padding: 10px; border: 2px solid var(--nevada); border-radius: 6px; font-size: 14px; background: var(--white); transition: border-color 0.3s;">
                                </div>
                                <div style="display: flex; flex-direction: column;">
                                    <label for="muscle-mass" style="font-size: 13px; color: var(--black-pearl); margin-bottom: 6px; font-weight: 600;">Spiermassa niveau</label>
                                    <select id="muscle-mass" name="muscle-mass" required aria-label="Selecteer je spiermassa niveau" style="padding: 10px; border: 2px solid var(--nevada); border-radius: 6px; font-size: 14px; background: var(--white); transition: border-color 0.3s;">
                                        <option value="">Selecteer niveau</option>
                                        <option value="0.97">Laag (weinig spiermassa)</option>
                                        <option value="1.00">Gemiddeld (normaal)</option>
                                        <option value="1.05">Hoog (veel spieren)</option>
                                        <option value="1.10">Zeer hoog (bodybuilder)</option>
                                    </select>
                                </div>
                            </div>
                        </fieldset>

                        <!-- Doelen & Activiteit Fieldset -->
                        <fieldset style="grid-column: 1 / -1; border: 1px solid var(--nevada); border-radius: 8px; padding: 16px; background: rgba(255,255,255,0.3);">
                            <legend style="color: var(--black-pearl); font-weight: 700; font-size: 16px; padding: 0 8px;">🎯 Doelen & Activiteit</legend>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                                <div style="display: flex; flex-direction: column;">
                                    <label for="goal" style="font-size: 13px; color: var(--black-pearl); margin-bottom: 6px; font-weight: 600;">Je doel</label>
                                    <select id="goal" name="goal" required aria-label="Selecteer je doel" style="padding: 10px; border: 2px solid var(--nevada); border-radius: 6px; font-size: 14px; background: var(--white); transition: border-color 0.3s;">
                                        <option value="">Selecteer doel</option>
                                        <option value="lose">Afvallen (-20%)</option>
                                        <option value="maintain">Gewicht behouden</option>
                                        <option value="gain">Aankomen (+15%)</option>
                                    </select>
                                </div>
                                <div style="display: flex; flex-direction: column;">
                                    <label for="activity" style="font-size: 13px; color: var(--black-pearl); margin-bottom: 6px; font-weight: 600;">Activiteitsniveau</label>
                                    <select id="activity" name="activity" required aria-label="Selecteer je activiteitsniveau" style="padding: 10px; border: 2px solid var(--nevada); border-radius: 6px; font-size: 14px; background: var(--white); transition: border-color 0.3s;">
                                        <option value="">Selecteer niveau</option>
                                        <option value="1.2">Zeer licht (kantoorwerk)</option>
                                        <option value="1.375">Licht actief (1-3x/week)</option>
                                        <option value="1.55">Matig actief (3-5x/week)</option>
                                        <option value="1.725">Erg actief (6-7x/week)</option>
                                        <option value="1.9">Zeer actief (2x/dag)</option>
                                    </select>
                                </div>
                            </div>
                        </fieldset>
                    </form>
                </div>

                <!-- Hidden BMI indicator for JavaScript compatibility -->
                <div id="main-bmi-indicator" style="display: none;">
                    <div class="bmi-placeholder"></div>
                </div>
            </div>

            <!-- Blogs & Producten (Rechts) -->
            <div style="width: 250px; background-color: var(--loblolly); border: 2px solid var(--nevada); border-radius: 10px; box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1); padding: 20px; flex-shrink: 0;">
                <!-- Recente Blogs -->
                <div style="margin-bottom: 30px;">
                    <h2 style="font-size: 18px; font-weight: 700; margin-bottom: 10px; color: var(--black-pearl); border-bottom: 1px solid var(--nevada); padding-bottom: 5px;">Recente Blogs</h2>
                    <div class="faq-item">
                        <h3>Placeholder Blog 1</h3>
                        <p>Hier komen de meest recente blog artikelen over supplementen, voeding en fitness.</p>
                    </div>
                    <div class="faq-item">
                        <h3>Placeholder Blog 2</h3>
                        <p>Tips en tricks voor optimale resultaten met je supplementen.</p>
                    </div>
                </div>

                <!-- Gerelateerde Producten -->
                <div>
                    <h2 style="font-size: 18px; font-weight: 700; margin-bottom: 10px; color: var(--black-pearl); border-bottom: 1px solid var(--nevada); padding-bottom: 5px;">Aanbevolen Producten</h2>
                    <div class="faq-item">
                        <h3>Whey Proteïne</h3>
                        <p>Perfect voor spieropbouw en herstel na je training.</p>
                    </div>
                    <div class="faq-item">
                        <h3>Creatine Monohydraat</h3>
                        <p>Verhoog je kracht en prestaties in de sportschool.</p>
                    </div>
                </div>
            </div>
        </div>


    </div>



    <!-- Clean Bronnen & Betrouwbaarheid Section -->
    <div style="background: var(--loblolly); padding: 40px 0; margin-top: 40px;">
        <div class="content-container">
            <!-- Zelfde drie-kolommen layout als calculator -->
            <div style="display: flex; gap: 20px;">

                <!-- Linker kolom: Lichte achtergrond -->
                <div style="width: 250px; background-color: var(--white); border: 2px solid var(--nevada); border-radius: 10px; box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1); padding: 20px; flex-shrink: 0;">
                    <h2 style="font-size: 16px; font-weight: 700; margin-bottom: 20px; color: var(--black-pearl);">🔬 Wetenschappelijke Basis</h2>
                    <div style="margin-bottom: 16px;">
                        <h3 style="font-size: 14px; font-weight: 600; color: var(--black-pearl); margin-bottom: 6px;">Mifflin-St Jeor Formule</h3>
                        <p style="font-size: 13px; color: var(--nevada); line-height: 1.5; margin: 0;">De meest accurate formule voor BMR berekening, erkend door internationale voedingsorganisaties.</p>
                    </div>
                    <div>
                        <h3 style="font-size: 14px; font-weight: 600; color: var(--black-pearl); margin-bottom: 6px;">Harris-Benedict Methode</h3>
                        <p style="font-size: 13px; color: var(--nevada); line-height: 1.5; margin: 0;">Bewezen activiteitsfactoren voor TDEE berekening, gebruikt door professionals wereldwijd.</p>
                    </div>
                </div>

                <!-- Midden kolom: Lichte achtergrond met contrast -->
                <div style="flex: 1; background-color: var(--white); border: 2px solid var(--nevada); border-radius: 10px; box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1); padding: 30px;">
                    <!-- Header met contrast -->
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: var(--black-pearl); margin: 0; font-size: 24px; font-weight: 700;">Bronnen & Betrouwbaarheid</h1>
                        <p style="color: var(--nevada); margin: 8px 0 0 0; font-size: 14px;">Wetenschappelijke onderbouwing</p>
                    </div>

                    <!-- Richtlijnen - clean styling -->
                    <div style="margin-bottom: 25px;">
                        <h3 style="color: var(--black-pearl); margin-bottom: 15px; font-size: 16px; font-weight: 600;">📊 Richtlijnen & Standaarden</h3>
                        <p style="color: var(--nevada); margin-bottom: 12px; font-size: 14px;">Macro verdelingen gebaseerd op:</p>
                        <div style="display: grid; gap: 8px;">
                            <div style="color: var(--black-pearl); font-size: 14px; line-height: 1.5;">
                                • <strong>Voedingscentrum</strong> - Nederlandse voedingsrichtlijnen
                            </div>
                            <div style="color: var(--black-pearl); font-size: 14px; line-height: 1.5;">
                                • <strong>ACSM</strong> - American College of Sports Medicine
                            </div>
                            <div style="color: var(--black-pearl); font-size: 14px; line-height: 1.5;">
                                • <strong>WHO</strong> - World Health Organization
                            </div>
                        </div>
                    </div>

                    <!-- Betrouwbaarheid - clean styling -->
                    <div>
                        <h3 style="color: var(--black-pearl); margin-bottom: 15px; font-size: 16px; font-weight: 600;">✅ Betrouwbaarheid</h3>
                        <p style="color: var(--nevada); margin: 0; font-size: 14px; line-height: 1.6;">Deze calculator gebruikt <strong style="color: var(--black-pearl);">wetenschappelijk bewezen formules</strong> die door voedingsdeskundigen en artsen wereldwijd worden gebruikt voor calorie- en macroberekeningen.</p>
                    </div>
                </div>

                <!-- Rechter kolom: Lichte achtergrond -->
                <div style="width: 250px; background-color: var(--white); border: 2px solid var(--nevada); border-radius: 10px; box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1); padding: 20px; flex-shrink: 0;">
                    <h2 style="font-size: 16px; font-weight: 700; margin-bottom: 20px; color: var(--black-pearl);">⚠️ Belangrijke Info</h2>
                    <div style="margin-bottom: 16px;">
                        <h3 style="font-size: 14px; font-weight: 600; color: var(--black-pearl); margin-bottom: 6px;">Disclaimer</h3>
                        <p style="font-size: 13px; color: var(--nevada); line-height: 1.5; margin: 0;">Deze tool is een <strong style="color: var(--black-pearl);">schatting</strong>, geen medisch advies. Raadpleeg bij twijfel een diëtist of arts.</p>
                    </div>
                    <div>
                        <h3 style="font-size: 14px; font-weight: 600; color: var(--black-pearl); margin-bottom: 6px;">Individualisatie</h3>
                        <p style="font-size: 13px; color: var(--nevada); line-height: 1.5; margin: 0;">Resultaten kunnen variëren per persoon. <strong style="color: var(--black-pearl);">Luister naar je lichaam</strong> en pas aan op basis van je resultaten.</p>
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- Professional 4-Column Grid Footer -->
    <footer class="site-footer" style="margin-top: 0;">
        <div class="footer-wrapper">
            <div class="footer-container">
            <!-- Single Grid Container for Both Rows -->
            <div class="footer-grid">

                <!-- ROW 1 - Main Navigation Sections -->

                <!-- Column 1: SuppRadar Brand -->
                <div class="footer-brand">
                    <div class="brand-header">
                        <h3 class="brand-title">SuppRadar</h3>
                        <span class="brand-badge">Onafhankelijk</span>
                    </div>
                    <p class="brand-description">
                        Jouw betrouwbare, onafhankelijke gids in de wereld van voedingssupplementen. Wij verkopen geen eigen producten, maar helpen je de beste keuzes te maken op basis van wetenschappelijk onderzoek en eerlijke reviews.
                    </p>
                    <div class="trust-indicators">
                        <div class="trust-item">
                            <span class="trust-icon">✓</span>
                            <span class="trust-text">Wetenschappelijk onderbouwd</span>
                        </div>
                        <div class="trust-item">
                            <span class="trust-icon">✓</span>
                            <span class="trust-text">Onafhankelijke reviews</span>
                        </div>
                    </div>
                </div>

                <!-- Column 2: Navigatie -->
                <div class="footer-column footer-nav-col">
                    <h4 class="footer-title">Navigatie</h4>
                    <nav class="footer-nav">
                        <a href="./index.html" class="footer-link">
                            <span class="link-arrow">→</span>Home
                        </a>
                        <a href="./supplementen.html" class="footer-link">
                            <span class="link-arrow">→</span>Supplementen
                        </a>
                        <a href="./calorie-calculator.html" class="footer-link">
                            <span class="link-arrow">→</span>Calorie Calculator
                        </a>
                        <a href="/reviews.html" class="footer-link">
                            <span class="link-arrow">→</span>Reviews
                        </a>
                    </nav>
                </div>

                <!-- Column 3: Juridisch -->
                <div class="footer-column footer-legal-col">
                    <h4 class="footer-title">Juridisch</h4>
                    <nav class="footer-nav">
                        <a href="/privacy.html" class="footer-link secondary">Privacybeleid</a>
                        <a href="/voorwaarden.html" class="footer-link secondary">Algemene Voorwaarden</a>
                        <a href="/cookies.html" class="footer-link secondary">Cookie Beleid</a>
                        <a href="/disclaimer.html" class="footer-link secondary">Disclaimer</a>
                    </nav>
                </div>

                <!-- Column 4: Contact (rechts uitgelijnd) -->
                <div class="footer-column footer-contact-col">
                    <div class="contact-section-wrapper">
                        <h4 class="footer-title">Contact</h4>
                        <div class="contact-section">
                            <a href="mailto:<EMAIL>" class="contact-email">
                                <span class="email-icon">✉</span>
                                <EMAIL>
                            </a>
                            <div class="contact-info">
                                <div class="contact-item">
                                    <strong>Reactietijd:</strong> binnen 24 uur
                                </div>
                                <div class="contact-item">
                                    <strong>Werkdagen:</strong> Ma-Vr 9:00-17:00
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- ROW 2 - Secondary Content Sections -->

                <!-- Column 1: Affiliate Disclaimer -->
                <div class="footer-section affiliate-disclaimer">
                    <div class="disclaimer-content">
                        <div class="disclaimer-text">
                            <h5 class="disclaimer-title">Affiliate Disclaimer <span class="disclaimer-icon">⚠</span></h5>
                            <p class="disclaimer-description">
                                SuppRadar bevat affiliate links. Als je via onze links koopt, ontvangen wij een kleine commissie zonder extra kosten voor jou.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Column 2-3: Newsletter (gecentreerd tussen kolom 2 en 3 van rij 1) -->
                <div class="footer-section newsletter-section">
                    <div class="newsletter-content">
                        <h5 class="newsletter-title">Mis geen supplement review meer <span class="newsletter-icon">📧</span></h5>
                        <p class="newsletter-description">Ontvang wekelijks onafhankelijke reviews en wetenschappelijk onderbouwde tips</p>
                        <div class="newsletter-form">
                            <input type="email" placeholder="Jouw e-mailadres" class="newsletter-input" aria-label="E-mailadres voor nieuwsbrief" required>
                            <button class="newsletter-button" type="submit" aria-label="Aanmelden voor nieuwsbrief">Gratis Aanmelden</button>
                        </div>
                        <div class="newsletter-note">✓ Geen spam ✓ Uitschrijven altijd mogelijk ✓ 100% gratis</div>
                    </div>
                </div>

                <!-- Column 4: Bedrijfsgegevens (op dezelfde hoogte als newsletter) -->
                <div class="footer-section business-info-section">
                    <div class="business-info-wrapper">
                        <div class="business-title">Bedrijfsgegevens</div>
                        <div class="business-details">
                            <div>KvK: 87654321</div>
                            <div>BTW: NL123456789B01</div>
                            <div>Gevestigd in Nederland</div>
                        </div>
                    </div>
                </div>


            </div>

            <!-- Horizontal Line + Copyright -->
            <div class="footer-bottom">
                <div class="footer-copyright">
                    © 2025 SuppRadar - Alle rechten voorbehouden – Gemaakt met ❤️ in Nederland
                </div>
            </div>
        </div>
        </div>
    </footer>

    <script src="main.js"></script>
</body>
</html>

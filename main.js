// ===== SHARED JAVASCRIPT FOR ALL PAGES =====

// ===== SHARED MOBILE MENU FUNCTIONALITY =====
function initializeMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navMenu = document.getElementById('nav-menu');

    if (mobileMenuToggle && navMenu) {
        mobileMenuToggle.addEventListener('click', function() {
            this.classList.toggle('active');
            navMenu.classList.toggle('active');
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!mobileMenuToggle.contains(event.target) && !navMenu.contains(event.target)) {
                mobileMenuToggle.classList.remove('active');
                navMenu.classList.remove('active');
            }
        });

        // Close menu when pressing Escape
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape' && navMenu.classList.contains('active')) {
                mobileMenuToggle.classList.remove('active');
                navMenu.classList.remove('active');
            }
        });
    }
}

// ===== INDEX PAGE SPECIFIC FUNCTIONALITY =====
function initializeIndexPage() {
    // Dynamisch huidige jaar toevoegen aan de footer
    const yearElement = document.getElementById('current-year');
    if (yearElement) {
        yearElement.textContent = new Date().getFullYear();
    }
}

// ===== SUPPLEMENTEN PAGE SPECIFIC FUNCTIONALITY =====

function initializeSupplementenPage() {
    const sortMenu = document.getElementById('sort-menu');
    const tbody = document.querySelector('.product-table tbody');
    let categoryCheckboxes = document.querySelectorAll('input[name="category"]');
    const ratingRadios = document.querySelectorAll('input[name="rating"]');

    // Elementen voor de prijsfilter
    const minPriceInputField = document.getElementById('min-price-input');
    const minPriceRange = document.getElementById('min-price-range');
    const maxPriceInputRange = document.getElementById('max-price-range');
    const maxPriceInputField = document.getElementById('max-price-input');
    const minHandle = document.getElementById('min-handle');
    const maxHandle = document.getElementById('max-handle');
    const sliderTrack = document.getElementById('price-slider-track');

    // Flags voor handmatige max prijs wijzigingen
    let maxPriceEditing = false;
    let hasManuallyChangedMaxPrice = false;

    // Bereken globale max eerst en rond af voor pijltjes
    const allRows = Array.from(tbody.querySelectorAll('tr'));
    const originalPrices = allRows.map(row => parseFloat(row.dataset.price));
    const globalMax = Math.ceil(Math.max(...originalPrices)); // Rond omhoog naar geheel getal

    function noFiltersApplied() {
        const anyCategory = Array.from(categoryCheckboxes).some(cb => cb.checked);
        const ratingValue = parseFloat(document.querySelector('input[name="rating"]:checked').value);
        return (!anyCategory && ratingValue === 0);
    }

    // Max price input handlers - aangepast om pijltjes niet te blokkeren
    maxPriceInputField.addEventListener('focus', () => {
        maxPriceEditing = true;
    });

    // Gebruik alleen 'input' event - dit vangt ALLE wijzigingen op (typen, pijltjes, etc.)
    maxPriceInputField.addEventListener('input', function() {
        if (this.value === "" || this.value === "0") {
            // Reset naar automatische modus als veld leeg wordt gemaakt
            hasManuallyChangedMaxPrice = false;
            updateSliderMax(); // Herbereken automatische max
            filterProducts();
            updateActiveFilters();
        } else {
            // Markeer als handmatig gewijzigd (ook bij pijltjes gebruik)
            hasManuallyChangedMaxPrice = true;
            const value = Math.round(parseFloat(this.value));
            if (!isNaN(value)) {
                maxPriceInputRange.value = value;
                updateSliderUI();
                filterProducts();
                updateActiveFilters();
            }
        }
    });

    maxPriceInputField.addEventListener('blur', function() {
        maxPriceEditing = false;
        if (this.value === "" || this.value === "0") {
            hasManuallyChangedMaxPrice = false;
            updateSliderMax();
        }
        updateSliderUI();
    });

    // Mouse wheel support voor max price
    maxPriceInputField.addEventListener('wheel', function(e) {
        if (document.activeElement === this) {
            e.preventDefault();
            let currentValue = parseInt(this.value) || 0;
            if (e.deltaY < 0) currentValue++; // Scroll up = increase
            if (e.deltaY > 0) currentValue--; // Scroll down = decrease

            // Zorg dat de waarde binnen de grenzen blijft
            const minValue = parseInt(minPriceInputField.value) || 0;
            const maxPossible = parseInt(maxPriceInputRange.max) || globalMax;
            currentValue = Math.max(minValue, Math.min(maxPossible, currentValue));

            this.value = currentValue;
            maxPriceInputRange.value = currentValue;
            hasManuallyChangedMaxPrice = true;
            updateSliderUI();
            filterProducts();
        }
    });

    // Min price input met pijltjes ondersteuning
    minPriceInputField.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
            e.preventDefault();
            let currentValue = parseInt(this.value) || 0;
            if (e.key === 'ArrowUp') currentValue++;
            if (e.key === 'ArrowDown') currentValue--;

            // Zorg dat de waarde binnen de grenzen blijft
            const maxValue = parseInt(maxPriceInputField.value) || parseInt(maxPriceInputRange.max) || globalMax;
            currentValue = Math.max(0, Math.min(maxValue, currentValue));

            this.value = currentValue === 0 ? "" : currentValue;
            minPriceRange.value = currentValue;
            updateSliderUI();
            filterProducts();
        }
    });

    // Initialisatie
    maxPriceInputRange.max = globalMax;
    maxPriceInputRange.value = globalMax;
    minPriceRange.max = globalMax;
    minPriceRange.value = 0;

    if (maxPriceInputField) {
        maxPriceInputField.value = globalMax;
        maxPriceInputField.setAttribute("max", globalMax);
        maxPriceInputField.setAttribute("min", "1");
    }
    if (minPriceInputField) {
        minPriceInputField.value = "";
        minPriceInputField.setAttribute("max", globalMax);
        minPriceInputField.setAttribute("min", "0");
    }

    function updateResultsCount() {
        const visibleRows = allRows.filter(row => row.style.display !== "none");
        document.getElementById('results-count').textContent = visibleRows.length;

        // Update mobile filter button count
        if (mobileResultsCount) {
            mobileResultsCount.textContent = visibleRows.length;
        }
    }

    function updateSliderMax() {
        // Alleen updaten als NIET handmatig gewijzigd
        if (maxPriceEditing || hasManuallyChangedMaxPrice) return;

        // Eerst alle non-price filters toepassen om te zien welke rijen zichtbaar zijn
        const selectedCategories = Array.from(categoryCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.value);
        const selectedRating = parseFloat(document.querySelector('input[name="rating"]:checked').value);

        // Filter rijen op basis van category en rating (niet prijs)
        const filteredRows = allRows.filter(row => {
            const categoryMatch = selectedCategories.length === 0 || selectedCategories.includes(row.dataset.category);
            const ratingMatch = parseFloat(row.dataset.rating) >= selectedRating;
            return categoryMatch && ratingMatch;
        });

        let newCalculatedMax = globalMax;
        if (filteredRows.length > 0) {
            newCalculatedMax = Math.ceil(Math.max(...filteredRows.map(row => parseFloat(row.dataset.price))));
        }

        const currentMin = parseFloat(minPriceRange.value) || 0;
        if (newCalculatedMax < currentMin) {
            newCalculatedMax = currentMin;
        }

        maxPriceInputRange.max = newCalculatedMax;
        maxPriceInputRange.value = newCalculatedMax;

        if (maxPriceInputField) {
            maxPriceInputField.value = newCalculatedMax;
        }

        updateSliderUI();
    }

    function updateSliderUI() {
        const sliderWidth = sliderTrack.offsetWidth;
        const currentMax = parseFloat(maxPriceInputRange.max);
        const minVal = parseFloat(minPriceRange.value) || 0;
        const maxVal = parseFloat(maxPriceInputRange.value);
        const minPercent = minVal / currentMax;
        const maxPercent = maxVal / currentMax;

        minHandle.style.left = (minVal === 0 ? 0 : minPercent * sliderWidth - minHandle.offsetWidth / 2) + "px";
        maxHandle.style.left = (maxVal === currentMax ? sliderWidth : maxPercent * sliderWidth - maxHandle.offsetWidth / 2) + "px";

        if (maxPriceInputField && !maxPriceEditing && !hasManuallyChangedMaxPrice) {
            maxPriceInputField.value = maxPriceInputRange.value;
        }
    }

    function filterProducts() {
        updateSliderMax();
        const selectedCategories = Array.from(categoryCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.value);
        const minPrice = minPriceInputField.value ? parseFloat(minPriceInputField.value) : 0;

        // Gebruik altijd de waarde uit het input veld
        const maxPrice = parseFloat(maxPriceInputField.value) || globalMax;

        const selectedRating = parseFloat(document.querySelector('input[name="rating"]:checked').value);

        allRows.forEach(row => {
            const price = parseFloat(row.dataset.price);
            let show = true;

            if (selectedCategories.length > 0 && !selectedCategories.includes(row.dataset.category)) {
                show = false;
            }
            if (price < minPrice || price > maxPrice) {
                show = false;
            }
            if (parseFloat(row.dataset.rating) < selectedRating) {
                show = false;
            }
            row.style.display = show ? '' : 'none';
        });

        sortProducts();
        updateResultsCount();
        updateMobileFilterButtonText();
    }

    function sortProducts() {
        const sortValue = sortMenu.value;
        allRows.sort((a, b) => {
            const priceA = parseFloat(a.dataset.price);
            const priceB = parseFloat(b.dataset.price);
            const ratingA = parseFloat(a.dataset.rating);
            const ratingB = parseFloat(b.dataset.rating);

            if (sortValue === 'price-low-high') return priceA - priceB;
            if (sortValue === 'price-high-low') return priceB - priceA;
            if (sortValue === 'rating') return ratingB - ratingA;
            return 0;
        });

        tbody.replaceChildren(...allRows);
    }

    // Slider interactie
    let activeHandle = null;

    function pointerMoveHandler(e) {
        if (!activeHandle) return;
        const sliderRect = sliderTrack.getBoundingClientRect();
        const sliderWidth = sliderRect.width;
        let newPercent = (e.clientX - sliderRect.left) / sliderWidth;
        newPercent = Math.max(0, Math.min(1, newPercent));

        const currentMax = parseFloat(maxPriceInputRange.max);
        if (activeHandle === minHandle) {
            let newValue = Math.round(newPercent * currentMax);
            const currentRangeMax = parseFloat(maxPriceInputRange.value);
            if (newValue > currentRangeMax) newValue = currentRangeMax;
            minPriceRange.value = newValue;
            minPriceInputField.value = newValue > 0 ? newValue : "";
        } else if (activeHandle === maxHandle) {
            let newValue = Math.round(newPercent * currentMax);
            const currentMin = minPriceInputField.value ? parseFloat(minPriceInputField.value) : 0;
            if (newValue < currentMin) newValue = currentMin;
            maxPriceInputRange.value = newValue;
            if (maxPriceInputField) {
                maxPriceInputField.value = newValue;
            }
            hasManuallyChangedMaxPrice = true;
        }

        updateSliderUI();
        filterProducts();
        updateActiveFilters();
    }

    function pointerUpHandler() {
        activeHandle = null;
        window.removeEventListener('pointermove', pointerMoveHandler);
        window.removeEventListener('pointerup', pointerUpHandler);
    }

    minHandle.addEventListener('pointerdown', function(e) {
        activeHandle = minHandle;
        window.addEventListener('pointermove', pointerMoveHandler);
        window.addEventListener('pointerup', pointerUpHandler);
        e.preventDefault();
    });

    maxHandle.addEventListener('pointerdown', function(e) {
        activeHandle = maxHandle;
        window.addEventListener('pointermove', pointerMoveHandler);
        window.addEventListener('pointerup', pointerUpHandler);
        e.preventDefault();
    });

    // Overige event listeners
    window.addEventListener('resize', updateSliderUI);

    minPriceInputField.addEventListener('input', function() {
        let value = parseFloat(this.value) || 0;
        value = Math.max(0, value);
        const currentMax = parseFloat(maxPriceInputRange.value);
        if (value > currentMax) value = currentMax;
        this.value = value === 0 ? "" : value;
        minPriceRange.value = value;
        updateSliderUI();
        filterProducts();
        updateActiveFilters();
    });

    maxPriceInputRange.addEventListener('input', function() {
        const minValue = minPriceInputField.value ? parseFloat(minPriceInputField.value) : 0;
        if (parseFloat(this.value) < minValue) {
            this.value = minValue;
        }
        updateSliderUI();
        filterProducts();
        updateActiveFilters();
    });

    minPriceRange.addEventListener('input', function() {
        if (minPriceInputField) {
            minPriceInputField.value = this.value === "0" ? "" : this.value;
        }
        updateSliderUI();
        filterProducts();
        updateActiveFilters();
    });

    function updateCategoryCounts() {
        const counts = {};
        allRows.forEach(row => {
            const category = row.dataset.category;
            counts[category] = (counts[category] || 0) + 1;
        });

        document.querySelectorAll('.category-count').forEach(span => {
            const category = span.dataset.category;
            span.textContent = `(${counts[category] || 0})`;
        });
    }

    // Active filters functionality
    const activeFiltersSection = document.getElementById('active-filters-section');
    const activeFiltersList = document.getElementById('active-filters-list');
    const resetFiltersBtn = document.getElementById('reset-filters-btn');

    function updateActiveFilters() {
        const activeFilters = [];

        // Check categories - combine multiple categories into one filter item
        const selectedCategories = Array.from(categoryCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.value);

        if (selectedCategories.length > 0) {
            if (selectedCategories.length === 1) {
                activeFilters.push(`Categorieën: ${selectedCategories[0]}`);
            } else {
                activeFilters.push(`Categorieën: ${selectedCategories.join(', ')}`);
            }
        }

        // Check rating
        const selectedRating = parseFloat(document.querySelector('input[name="rating"]:checked').value);
        if (selectedRating > 0) {
            activeFilters.push(`Minimale rating: ${selectedRating} sterren`);
        }

        // Check price range - show if any price filter is active
        const minPrice = minPriceInputField.value ? parseFloat(minPriceInputField.value) : 0;
        const maxPrice = parseFloat(maxPriceInputField.value) || globalMax;

        // Show price filter if min price is set OR max price has been manually changed
        if (minPrice > 0 || (hasManuallyChangedMaxPrice && maxPrice < globalMax)) {
            activeFilters.push(`Prijs: €${minPrice} - €${maxPrice}`);
        }

        // Update display
        if (activeFilters.length > 0) {
            activeFiltersList.innerHTML = activeFilters.map(filter =>
                `<span class="active-filter-item">${filter}</span>`
            ).join('');
            activeFiltersSection.style.display = 'block';
        } else {
            activeFiltersSection.style.display = 'none';
        }
    }

    function resetAllFilters() {
        // Reset categories
        categoryCheckboxes.forEach(cb => cb.checked = false);

        // Reset rating to default (0)
        document.querySelector('input[name="rating"][value="0"]').checked = true;

        // Reset price range
        minPriceRange.value = 0;
        maxPriceInputRange.value = globalMax;
        if (minPriceInputField) minPriceInputField.value = '';
        if (maxPriceInputField) maxPriceInputField.value = globalMax;

        // Reset manual price change flag
        hasManuallyChangedMaxPrice = false;

        // Update UI
        updateSliderUI();
        updateSliderMax();
        filterProducts();
        updateActiveFilters();
    }

    // Add event listener for reset button
    resetFiltersBtn.addEventListener('click', resetAllFilters);

    // Function to add event listeners to category checkboxes
    function addCategoryEventListeners() {
        categoryCheckboxes.forEach(cb => cb.addEventListener('change', () => {
            filterProducts();
            updateActiveFilters();
        }));
    }

    // Initialisatie
    updateCategoryCounts();
    updateActiveFilters();
    sortMenu.addEventListener('change', sortProducts);
    addCategoryEventListeners();
    ratingRadios.forEach(radio => radio.addEventListener('change', () => {
        filterProducts();
        updateActiveFilters();
    }));

    // Activeer categorie via URL parameter
    const params = new URLSearchParams(window.location.search);
    const queryCategory = params.get('category');
    if (queryCategory) {
        const checkbox = document.querySelector(`input[name="category"][value="${queryCategory}"]`);
        if (checkbox) {
            checkbox.checked = true;
            checkbox.dispatchEvent(new Event('change', { bubbles: true }));
        }
        // Update active filters display after URL parameter processing
        updateActiveFilters();
    }

    // Mobile detection
    const filterMenu = document.getElementById('filter-menu');
    const isMobile = () => window.innerWidth <= 768;

    // Mobile filter button elements
    const mobileFilterButtonContainer = document.getElementById('mobile-filter-button-container');
    const mobileFilterButton = document.getElementById('mobile-filter-button');
    const mobileResultsCount = document.getElementById('mobile-results-count');

    // Show/hide mobile filter button based on screen size
    function updateMobileFilterButtonVisibility() {
        if (isMobile()) {
            mobileFilterButtonContainer.style.display = 'block';
        } else {
            mobileFilterButtonContainer.style.display = 'none';
        }
    }

    // Add click event to mobile filter button
    if (mobileFilterButton) {
        mobileFilterButton.addEventListener('click', showMobileFilters);
    }

    function showMobileFilters() {
        if (isMobile()) {
            filterMenu.classList.add('mobile-overlay');
            addMobileFilterButton();

            // Update slider UI after overlay is shown to ensure correct positioning
            setTimeout(() => {
                updateSliderUI();
            }, 100);
        }
    }

    function hideMobileFilters() {
        filterMenu.classList.remove('mobile-overlay');
        removeMobileFilterButton();
    }

    function addMobileFilterButton() {
        // Remove existing button if any
        removeMobileFilterButton();

        // Add close button (kruisje) to filter overlay
        const closeButton = document.createElement('button');
        closeButton.className = 'mobile-filter-close';
        closeButton.innerHTML = '×';
        closeButton.addEventListener('click', hideMobileFilters);

        const filterContent = document.getElementById('filter-content');
        const applyButtonContainer = document.createElement('div');
        applyButtonContainer.className = 'mobile-filter-apply';
        applyButtonContainer.id = 'mobile-filter-apply';

        const applyButton = document.createElement('button');
        applyButton.textContent = 'Filter resultaten';
        applyButton.addEventListener('click', function() {
            updateResultsCount();
            const resultCount = document.getElementById('results-count').textContent;
            applyButton.textContent = `Filter ${resultCount} resultaten`;

            // Close filters after applying
            setTimeout(() => {
                hideMobileFilters();
            }, 300);
        });

        applyButtonContainer.appendChild(applyButton);
        filterMenu.insertBefore(closeButton, filterMenu.firstChild);
        filterMenu.appendChild(applyButtonContainer);

        // Update button text with current results
        updateMobileFilterButtonText();

        // Update slider UI after mobile overlay is fully rendered
        setTimeout(() => {
            updateSliderUI();
        }, 150);
    }

    function removeMobileFilterButton() {
        const existingButton = document.getElementById('mobile-filter-apply');
        if (existingButton) {
            existingButton.remove();
        }
        const existingCloseButton = document.querySelector('.mobile-filter-close');
        if (existingCloseButton) {
            existingCloseButton.remove();
        }
    }

    function updateMobileFilterButtonText() {
        const applyButton = document.querySelector('#mobile-filter-apply button');
        if (applyButton) {
            const resultCount = document.getElementById('results-count').textContent;
            applyButton.textContent = `Filter ${resultCount} resultaten`;
        }
    }

    // Set initial state - filters always visible on desktop, hidden on mobile
    function setInitialFilterState() {
        if (isMobile()) {
            // On mobile, filters are hidden by default
            filterMenu.classList.remove('mobile-overlay');
        } else {
            // On desktop, filters are always visible
            filterMenu.classList.remove('hidden', 'mobile-overlay');
        }
    }

    // Window resize handler
    window.addEventListener('resize', function() {
        const wasMobile = filterMenu.classList.contains('mobile-overlay');
        const nowMobile = isMobile();

        if (!nowMobile && wasMobile) {
            // Switching to desktop from mobile overlay - remove overlay
            filterMenu.classList.remove('mobile-overlay');
            removeMobileFilterButton();
        }

        // Update mobile filter button visibility
        updateMobileFilterButtonVisibility();
    });

    // Initialize
    setInitialFilterState();
    updateSliderUI();
    updateMobileFilterButtonVisibility();
    filterProducts();
}

// ===== CALORIE CALCULATOR PAGE SPECIFIC FUNCTIONALITY =====

function initializeCalorieCalculatorPage() {
    // Form submission handler
    const calorieForm = document.getElementById('calorie-form');
    if (calorieForm) {
        calorieForm.addEventListener('submit', function(e) {
            e.preventDefault();
            calculateCalories();
        });
    }

    // Live calculation on input changes
    const calculationInputs = ['gender', 'age', 'height', 'weight', 'activity', 'muscle-mass', 'goal'];
    calculationInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('change', function() {
                // Only calculate if all required fields are filled
                const allFieldsFilled = calculationInputs.every(id => {
                    const field = document.getElementById(id);
                    return field && field.value && field.value.trim() !== '';
                });

                if (allFieldsFilled) {
                    calculateCalories();
                }
            });
        }
    });

    // Add BMI indicator update listeners
    const bmiInputs = ['height', 'weight'];
    bmiInputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.addEventListener('input', updateBMIIndicator);
        }
    });
}

function updateBMIIndicator() {
    const height = parseInt(document.getElementById('height').value);
    const weight = parseFloat(document.getElementById('weight').value);

    const bmiDisplay = document.getElementById('sidebar-bmi-display');

    if (!bmiDisplay) return; // Exit if element doesn't exist

    if (!height || !weight) {
        bmiDisplay.innerHTML = `
            <div class="bmi-placeholder">
                <p>Vul je lengte en gewicht in om je BMI te zien</p>
            </div>
        `;
        return;
    }

    // Calculate BMI
    const bmi = weight / ((height / 100) ** 2);

    // Determine BMI category and color
    let bmiCategory = '';
    let bmiColor = '';

    if (bmi < 18.5) {
        bmiCategory = 'Ondergewicht';
        bmiColor = '#3498db';
    } else if (bmi < 25) {
        bmiCategory = 'Normaal gewicht';
        bmiColor = '#27ae60';
    } else if (bmi < 30) {
        bmiCategory = 'Overgewicht';
        bmiColor = '#f39c12';
    } else {
        bmiCategory = 'Obesitas';
        bmiColor = '#e74c3c';
    }

    // Calculate marker position (BMI scale from 15 to 35)
    const minBMI = 15;
    const maxBMI = 35;
    const clampedBMI = Math.max(minBMI, Math.min(maxBMI, bmi));
    const markerPosition = ((clampedBMI - minBMI) / (maxBMI - minBMI)) * 100;

    bmiDisplay.innerHTML = `
        <div class="bmi-scale">
            <div class="bmi-scale-bar">
                <div class="bmi-marker" style="left: ${markerPosition}%;">
                    <div class="bmi-value-display">${bmi.toFixed(1)}</div>
                </div>
            </div>
            <div class="bmi-labels">
                <span>15</span>
                <span>18.5</span>
                <span>25</span>
                <span>30</span>
                <span>35</span>
            </div>
            <div class="bmi-category-display" style="color: ${bmiColor};">
                ${bmiCategory}
            </div>
        </div>
    `;
}

function calculateCalories() {
    const gender = document.getElementById('gender').value;
    const age = parseInt(document.getElementById('age').value);
    const height = parseInt(document.getElementById('height').value);
    const weight = parseFloat(document.getElementById('weight').value);
    const activity = parseFloat(document.getElementById('activity').value);
    const muscleMass = parseFloat(document.getElementById('muscle-mass').value);
    const goal = document.getElementById('goal').value;

    if (!gender || !age || !height || !weight || !activity || !muscleMass || !goal) {
        // Hide results if not all fields are filled
        const resultContainer = document.getElementById('result-container');
        if (resultContainer) {
            resultContainer.style.display = 'none';
        }
        return;
    }

    // Calculate BMR using Mifflin-St Jeor Equation
    let bmr;
    if (gender === 'male') {
        bmr = 10 * weight + 6.25 * height - 5 * age + 5;
    } else {
        bmr = 10 * weight + 6.25 * height - 5 * age - 161;
    }

    // Adjust BMR based on muscle mass
    bmr *= muscleMass;

    const tdee = bmr * activity;

    let targetCalories;
    let goalText;
    switch (goal) {
        case 'lose':
            targetCalories = tdee * 0.8;
            goalText = 'om af te vallen';
            break;
        case 'maintain':
            targetCalories = tdee;
            goalText = 'om op gewicht te blijven';
            break;
        case 'gain':
            targetCalories = tdee * 1.15;
            goalText = 'om aan te komen';
            break;
    }

    // Calculate macros based on goal
    const macros = calculateMacros(targetCalories, goal);

    displayResults(bmr, tdee, targetCalories, goalText, macros);
}

function calculateMacros(calories, goal) {
    let proteinPercent, fatPercent, carbPercent;

    switch (goal) {
        case 'lose':
            proteinPercent = 30;
            fatPercent = 25;
            carbPercent = 45;
            break;
        case 'maintain':
            proteinPercent = 25;
            fatPercent = 30;
            carbPercent = 45;
            break;
        case 'gain':
            proteinPercent = 30;
            fatPercent = 25;
            carbPercent = 45;
            break;
        default:
            proteinPercent = 25;
            fatPercent = 30;
            carbPercent = 45;
    }

    // Calculate grams (protein: 4 kcal/g, fat: 9 kcal/g, carbs: 4 kcal/g)
    const proteinGrams = Math.round((calories * proteinPercent / 100) / 4);
    const fatGrams = Math.round((calories * fatPercent / 100) / 9);
    const carbGrams = Math.round((calories * carbPercent / 100) / 4);

    return {
        protein: { grams: proteinGrams, percent: proteinPercent },
        fat: { grams: fatGrams, percent: fatPercent },
        carbs: { grams: carbGrams, percent: carbPercent }
    };
}

function displayResults(bmr, tdee, targetCalories, goalText, macros) {
    const resultContainer = document.getElementById('result-container');
    const resultText = document.getElementById('result-text');
    const bmrValue = document.getElementById('bmr-value');
    const tdeeValue = document.getElementById('tdee-value');
    const targetCaloriesElement = document.getElementById('target-calories');
    const goalTextElement = document.getElementById('goal-text');

    // Toon alle resultaten
    const mainResult = document.getElementById('main-result');
    const bmrTdeeResults = document.getElementById('bmr-tdee-results');

    // Prominente totale calorieën
    if (targetCaloriesElement) {
        targetCaloriesElement.textContent = `${Math.round(targetCalories)} kcal`;
    }
    if (goalTextElement) {
        goalTextElement.textContent = goalText;
    }

    // Toon de hoofdresultaten
    if (mainResult) {
        mainResult.style.display = 'block';
    }
    if (bmrTdeeResults) {
        bmrTdeeResults.style.display = 'grid';
    }

    // Oude result text (als die er nog is)
    if (resultText) {
        resultText.innerHTML = `Je hebt ongeveer <strong>${Math.round(targetCalories)} kcal</strong> per dag nodig ${goalText}.`;
    }

    if (bmrValue) {
        bmrValue.textContent = `${Math.round(bmr)} kcal`;
    }
    if (tdeeValue) {
        tdeeValue.textContent = `${Math.round(tdee)} kcal`;
    }

    // Show and populate macros display
    const macrosContainer = document.getElementById('macros-container');
    if (macrosContainer) {
        macrosContainer.style.display = 'block'; // Toon de macros container
        macrosContainer.innerHTML = `
            <div style="background: rgba(255,255,255,0.7); padding: 20px; border-radius: 12px; border: 1px solid var(--nevada);">
                <h3 style="color: var(--black-pearl); margin-bottom: 15px; font-size: 16px; font-weight: 700; text-align: center;">🎯 Aanbevolen macro's per dag</h3>
            <div class="macros-grid">
                <div class="macro-item protein">
                    <div class="macro-icon">🥩</div>
                    <div class="macro-info">
                        <h4>Eiwitten</h4>
                        <p class="macro-amount">${macros.protein.grams}g</p>
                        <p class="macro-percent">${macros.protein.percent}% van calorieën</p>
                    </div>
                </div>
                <div class="macro-item fat">
                    <div class="macro-icon">🥑</div>
                    <div class="macro-info">
                        <h4>Vetten</h4>
                        <p class="macro-amount">${macros.fat.grams}g</p>
                        <p class="macro-percent">${macros.fat.percent}% van calorieën</p>
                    </div>
                </div>
                <div class="macro-item carbs">
                    <div class="macro-icon">🍞</div>
                    <div class="macro-info">
                        <h4>Koolhydraten</h4>
                        <p class="macro-amount">${macros.carbs.grams}g</p>
                        <p class="macro-percent">${macros.carbs.percent}% van calorieën</p>
                    </div>
                </div>
            </div>
            <div class="macro-explanation">
                <p><strong>💡 Waarom deze verdeling?</strong></p>
                    <p>• <strong>Eiwitten:</strong> Behouden spiermassa en verzadiging</p>
                    <p>• <strong>Vetten:</strong> Hormoonproductie en essentiële functies</p>
                    <p>• <strong>Koolhydraten:</strong> Energie voor training en herstel</p>
                </div>
            </div>
        `;
    }

    if (resultContainer) {
        resultContainer.style.display = 'block';
        resultContainer.scrollIntoView({ behavior: 'smooth' });
    }
}

// ===== BLOG PAGE SPECIFIC FUNCTIONALITY =====

// Sample blog data - in real implementation this would come from a CMS or API
const blogData = [
    {
        id: 1,
        title: "De Beste Eiwitpoeders van 2025: Wetenschappelijke Vergelijking",
        summary: "Een uitgebreide analyse van de populairste eiwitpoeders op de markt. Welke merken leveren de beste kwaliteit voor je geld?",
        category: "supplementen",
        date: "2025-01-15",
        readTime: "8 min",
        image: "./Afbeeldingen/blog-eiwit.webp",
        slug: "beste-eiwitpoeders-2025"
    },
    {
        id: 2,
        title: "Creatine: Alles Wat Je Moet Weten Over Dit Krachtige Supplement",
        summary: "Ontdek hoe creatine werkt, welke vorm het beste is en hoe je het optimaal gebruikt voor kracht en spiermassa.",
        category: "supplementen",
        date: "2025-01-12",
        readTime: "6 min",
        image: "./Afbeeldingen/blog-creatine.webp",
        slug: "creatine-complete-gids"
    },
    {
        id: 3,
        title: "Macro's Berekenen: De Complete Gids voor Beginners",
        summary: "Leer hoe je je macro's berekent voor optimale resultaten, of je nu wilt afvallen, aankomen of je gewicht wilt behouden.",
        category: "voeding",
        date: "2025-01-10",
        readTime: "10 min",
        image: "./Afbeeldingen/blog-macros.webp",
        slug: "macros-berekenen-gids"
    },
    {
        id: 4,
        title: "Pre-Workout Supplementen: Wat Werkt Echt?",
        summary: "Een eerlijke blik op pre-workout ingrediënten. Welke stoffen geven je daadwerkelijk meer energie en focus?",
        category: "supplementen",
        date: "2025-01-08",
        readTime: "7 min",
        image: "./Afbeeldingen/blog-preworkout.webp",
        slug: "pre-workout-wat-werkt"
    },
    {
        id: 5,
        title: "Krachttraining voor Beginners: De Basis Principes",
        summary: "Start je fitness journey met de juiste basis. Alles over sets, reps, progressie en de belangrijkste oefeningen.",
        category: "training",
        date: "2025-01-05",
        readTime: "12 min",
        image: "./Afbeeldingen/blog-krachttraining.webp",
        slug: "krachttraining-beginners"
    },
    {
        id: 6,
        title: "Spieropbouw na je 40e: Tips en Supplementen",
        summary: "Hoe je effectief spiermassa opbouwt en behoudt naarmate je ouder wordt. Specifieke tips en supplementen voor 40+.",
        category: "spieropbouw",
        date: "2025-01-03",
        readTime: "9 min",
        image: "./Afbeeldingen/blog-40plus.webp",
        slug: "spieropbouw-na-40"
    },
    {
        id: 7,
        title: "Intermittent Fasting en Supplementen: De Perfecte Combinatie",
        summary: "Hoe je intermittent fasting combineert met de juiste supplementen voor optimale resultaten bij gewichtsverlies.",
        category: "gewichtsverlies",
        date: "2025-01-01",
        readTime: "8 min",
        image: "./Afbeeldingen/blog-fasting.webp",
        slug: "intermittent-fasting-supplementen"
    },
    {
        id: 8,
        title: "Vitamine D: Het Meest Onderschatte Supplement",
        summary: "Waarom vitamine D cruciaal is voor je gezondheid, prestaties en hoe je een tekort voorkomt.",
        category: "supplementen",
        date: "2024-12-28",
        readTime: "6 min",
        image: "./Afbeeldingen/blog-vitamine-d.webp",
        slug: "vitamine-d-belang"
    },
    {
        id: 9,
        title: "Voeding Rond je Training: Timing is Alles",
        summary: "Ontdek wat je voor, tijdens en na je training moet eten voor optimale prestaties en herstel.",
        category: "voeding",
        date: "2024-12-25",
        readTime: "11 min",
        image: "./Afbeeldingen/blog-voeding-training.webp",
        slug: "voeding-rond-training"
    },
    {
        id: 10,
        title: "Sleep en Supplementen: Beter Herstel voor Betere Resultaten",
        summary: "Hoe slaap je prestaties beïnvloedt en welke supplementen kunnen helpen bij beter herstel en slaapkwaliteit.",
        category: "supplementen",
        date: "2024-12-22",
        readTime: "7 min",
        image: "./Afbeeldingen/blog-sleep.webp",
        slug: "sleep-supplementen-herstel"
    },
    {
        id: 11,
        title: "HIIT vs Cardio: Wat is Beter voor Gewichtsverlies?",
        summary: "Een wetenschappelijke vergelijking tussen HIIT en traditionele cardio voor effectief gewichtsverlies.",
        category: "training",
        date: "2024-12-20",
        readTime: "9 min",
        image: "./Afbeeldingen/blog-hiit-cardio.webp",
        slug: "hiit-vs-cardio-gewichtsverlies"
    },
    {
        id: 12,
        title: "Supplementen voor Vrouwen: Specifieke Behoeften",
        summary: "Welke supplementen zijn specifiek belangrijk voor vrouwen en hoe verschillen de behoeften van mannen?",
        category: "supplementen",
        date: "2024-12-18",
        readTime: "10 min",
        image: "./Afbeeldingen/blog-vrouwen.webp",
        slug: "supplementen-voor-vrouwen"
    }
];

let currentPage = 1;
const articlesPerPage = 6;
let filteredBlogs = [...blogData];

function initializeBlogPage() {
    renderBlogGrid();
    updateResultsCount();
    updatePagination();
}

function searchBlogs() {
    const searchTerm = document.getElementById('blog-search').value.toLowerCase();
    const selectedCategories = getSelectedCategories();

    filteredBlogs = blogData.filter(blog => {
        const matchesSearch = blog.title.toLowerCase().includes(searchTerm) ||
                            blog.summary.toLowerCase().includes(searchTerm);
        const matchesCategory = selectedCategories.length === 0 ||
                              selectedCategories.includes('alle') ||
                              selectedCategories.includes(blog.category);

        return matchesSearch && matchesCategory;
    });

    currentPage = 1;
    renderBlogGrid();
    updateResultsCount();
    updatePagination();
}

function filterBlogs() {
    const selectedCategories = getSelectedCategories();
    const searchTerm = document.getElementById('blog-search').value.toLowerCase();

    filteredBlogs = blogData.filter(blog => {
        const matchesSearch = blog.title.toLowerCase().includes(searchTerm) ||
                            blog.summary.toLowerCase().includes(searchTerm);
        const matchesCategory = selectedCategories.length === 0 ||
                              selectedCategories.includes('alle') ||
                              selectedCategories.includes(blog.category);

        return matchesSearch && matchesCategory;
    });

    currentPage = 1;
    renderBlogGrid();
    updateResultsCount();
    updatePagination();
}

function getSelectedCategories() {
    const checkboxes = document.querySelectorAll('.category-filters input[type="checkbox"]:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

function sortBlogs() {
    const sortValue = document.getElementById('sort-select').value;

    filteredBlogs.sort((a, b) => {
        switch (sortValue) {
            case 'newest':
                return new Date(b.date) - new Date(a.date);
            case 'oldest':
                return new Date(a.date) - new Date(b.date);
            case 'popular':
                // For now, sort by read time as proxy for popularity
                return parseInt(b.readTime) - parseInt(a.readTime);
            case 'alphabetical':
                return a.title.localeCompare(b.title);
            default:
                return 0;
        }
    });

    renderBlogGrid();
}

function renderBlogGrid() {
    const blogGrid = document.getElementById('blog-grid');
    const startIndex = (currentPage - 1) * articlesPerPage;
    const endIndex = startIndex + articlesPerPage;
    const blogsToShow = filteredBlogs.slice(startIndex, endIndex);

    blogGrid.innerHTML = blogsToShow.map(blog => `
        <article class="blog-card" onclick="openBlogPost('${blog.slug}')">
            <img src="${blog.image}" alt="${blog.title}" class="blog-image" onerror="this.src='./Afbeeldingen/blog-placeholder.webp'">
            <div class="blog-content">
                <span class="blog-category">${getCategoryDisplayName(blog.category)}</span>
                <h3 class="blog-title">${blog.title}</h3>
                <div class="blog-meta">
                    <span class="blog-date">${formatDate(blog.date)}</span>
                    <span class="blog-read-time">${blog.readTime} leestijd</span>
                </div>
                <p class="blog-summary">${blog.summary}</p>
                <a href="./blog-post.html?slug=${blog.slug}" class="read-more-btn">Lees meer →</a>
            </div>
        </article>
    `).join('');
}

function getCategoryDisplayName(category) {
    const categoryNames = {
        'supplementen': 'Supplementen',
        'voeding': 'Voeding',
        'training': 'Training',
        'spieropbouw': 'Spieropbouw',
        'gewichtsverlies': 'Gewichtsverlies'
    };
    return categoryNames[category] || category;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('nl-NL', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function updateResultsCount() {
    const count = filteredBlogs.length;
    document.getElementById('results-count').textContent = `${count} artikel${count !== 1 ? 'en' : ''} gevonden`;
}

function updatePagination() {
    const totalPages = Math.ceil(filteredBlogs.length / articlesPerPage);

    document.getElementById('current-page').textContent = currentPage;
    document.getElementById('total-pages').textContent = totalPages;

    const prevBtn = document.getElementById('prev-btn');
    const nextBtn = document.getElementById('next-btn');

    prevBtn.disabled = currentPage === 1;
    nextBtn.disabled = currentPage === totalPages || totalPages === 0;
}

function previousPage() {
    if (currentPage > 1) {
        currentPage--;
        renderBlogGrid();
        updatePagination();
        document.querySelector('.blog-main').scrollIntoView({ behavior: 'smooth' });
    }
}

function nextPage() {
    const totalPages = Math.ceil(filteredBlogs.length / articlesPerPage);
    if (currentPage < totalPages) {
        currentPage++;
        renderBlogGrid();
        updatePagination();
        document.querySelector('.blog-main').scrollIntoView({ behavior: 'smooth' });
    }
}

function openBlogPost(slug) {
    window.location.href = `./blog-post.html?slug=${slug}`;
}

// ===== MAIN INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function () {
    // Initialize mobile menu for all pages
    initializeMobileMenu();

    // Check which page we're on and initialize accordingly
    if (document.body.classList.contains('supplementen-page')) {
        // We're on the supplementen page
        initializeSupplementenPage();
    } else if (document.body.classList.contains('calculator-page')) {
        // We're on the calorie calculator page
        initializeCalorieCalculatorPage();
        initializeIndexPage(); // Also initialize index page functionality for footer year
    } else if (document.body.classList.contains('blog-page')) {
        // We're on the blog page
        initializeBlogPage();
        initializeIndexPage(); // Also initialize index page functionality for footer year
    } else {
        // We're on the index page or other pages
        initializeIndexPage();
    }
});
